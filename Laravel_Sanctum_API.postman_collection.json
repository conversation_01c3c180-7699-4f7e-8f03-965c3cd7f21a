{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Laravel Sanctum API", "description": "Complete API collection for Laravel Sanctum authentication and company management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"role_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('auth_token', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/user", "host": ["{{base_url}}"], "path": ["api", "auth", "user"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}, "response": []}, {"name": "Revoke All Tokens", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/revoke-all-tokens", "host": ["{{base_url}}"], "path": ["api", "auth", "revoke-all-tokens"]}}, "response": []}]}, {"name": "Test Endpoints", "item": [{"name": "Test Authentication", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/test", "host": ["{{base_url}}"], "path": ["api", "test"]}}, "response": []}]}, {"name": "Search & Filtering", "item": [{"name": "Search Companies by Keyword", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?search=tech", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "search", "value": "tech", "description": "Search across company name and description"}]}}, "response": []}, {"name": "Filter by Service Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?service_category=IT", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "service_category", "value": "IT", "description": "Available: IT, Marketing, Consulting, Design"}]}}, "response": []}, {"name": "Filter by Rating", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?rating=4.5", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "rating", "value": "4.5", "description": "Minimum rating (companies with rating >= 4.5)"}]}}, "response": []}, {"name": "Filter by Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?location=New York", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "location", "value": "New York", "description": "Partial location search (case-insensitive)"}]}}, "response": []}, {"name": "Advanced Combined Search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?search=tech&service_category=IT&rating=4&location=CA&sort_by=rating&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "search", "value": "tech", "description": "Keyword search in name/description"}, {"key": "service_category", "value": "IT", "description": "Service category filter"}, {"key": "rating", "value": "4", "description": "Minimum rating"}, {"key": "location", "value": "CA", "description": "Location contains 'CA'"}, {"key": "sort_by", "value": "rating", "description": "Sort by rating"}, {"key": "sort_order", "value": "desc", "description": "Descending order"}]}}, "response": []}]}, {"name": "Company Management", "item": [{"name": "Get All Companies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "search", "value": "tech", "description": "Search in company name and description", "disabled": true}, {"key": "service_category", "value": "IT", "description": "Filter by service type (IT, Marketing, Consulting, Design)", "disabled": true}, {"key": "rating", "value": "4", "description": "Minimum rating filter (1-5)", "disabled": true}, {"key": "location", "value": "New York", "description": "Partial location search", "disabled": true}, {"key": "sort_by", "value": "rating", "description": "Sort by: name, rating, location, created_at", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc", "disabled": true}]}}, "response": []}, {"name": "Search Companies by Keyword", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?search=tech", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "search", "value": "tech", "description": "Search across company name and description"}]}}, "response": []}, {"name": "Filter Companies by Service Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?service_category=IT", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "service_category", "value": "IT", "description": "Available: IT, Marketing, Consulting, Design"}]}}, "response": []}, {"name": "Filter Companies by Rating", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?rating=4.5", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "rating", "value": "4.5", "description": "Minimum rating (companies with rating >= 4.5)"}]}}, "response": []}, {"name": "Filter Companies by Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?location=New York", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "location", "value": "New York", "description": "Partial location search (case-insensitive)"}]}}, "response": []}, {"name": "Advanced Search & Filter", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies?search=tech&service_category=IT&rating=4&location=CA&sort_by=rating&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "search", "value": "tech", "description": "Keyword search in name/description"}, {"key": "service_category", "value": "IT", "description": "Service category filter"}, {"key": "rating", "value": "4", "description": "Minimum rating"}, {"key": "location", "value": "CA", "description": "Location contains 'CA'"}, {"key": "sort_by", "value": "rating", "description": "Sort by rating"}, {"key": "sort_order", "value": "desc", "description": "Descending order"}]}}, "response": []}, {"name": "Get Company by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Create Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New Tech Company\",\n    \"description\": \"A cutting-edge technology company\",\n    \"location\": \"Silicon Valley, CA\",\n    \"service_category\": \"IT\",\n    \"rating\": 4.8\n}"}, "url": {"raw": "{{base_url}}/api/companies", "host": ["{{base_url}}"], "path": ["api", "companies"]}}, "response": []}, {"name": "Update Company", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Tech Company\",\n    \"description\": \"An updated cutting-edge technology company\",\n    \"location\": \"San Francisco, CA\",\n    \"service_category\": \"IT\",\n    \"rating\": 4.9\n}"}, "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Delete Company", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Add User to Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2\n}"}, "url": {"raw": "{{base_url}}/api/companies/1/add-user", "host": ["{{base_url}}"], "path": ["api", "companies", "1", "add-user"]}}, "response": []}, {"name": "Remove User from Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2\n}"}, "url": {"raw": "{{base_url}}/api/companies/1/remove-user", "host": ["{{base_url}}"], "path": ["api", "companies", "1", "remove-user"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}]}