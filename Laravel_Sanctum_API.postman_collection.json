{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Laravel Sanctum API", "description": "Complete API collection for Laravel Sanctum authentication and company management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"role_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('auth_token', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/user", "host": ["{{base_url}}"], "path": ["api", "auth", "user"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}, "response": []}, {"name": "Revoke All Tokens", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/revoke-all-tokens", "host": ["{{base_url}}"], "path": ["api", "auth", "revoke-all-tokens"]}}, "response": []}]}, {"name": "Test Endpoints", "item": [{"name": "Test Authentication", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/test", "host": ["{{base_url}}"], "path": ["api", "test"]}}, "response": []}]}, {"name": "Company Management", "item": [{"name": "Get All Companies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies", "host": ["{{base_url}}"], "path": ["api", "companies"], "query": [{"key": "service_category", "value": "IT", "disabled": true}, {"key": "rating", "value": "4", "disabled": true}, {"key": "location", "value": "New York", "disabled": true}]}}, "response": []}, {"name": "Get Company by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Create Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"New Tech Company\",\n    \"description\": \"A cutting-edge technology company\",\n    \"location\": \"Silicon Valley, CA\",\n    \"service_category\": \"IT\",\n    \"rating\": 4.8\n}"}, "url": {"raw": "{{base_url}}/api/companies", "host": ["{{base_url}}"], "path": ["api", "companies"]}}, "response": []}, {"name": "Update Company", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Tech Company\",\n    \"description\": \"An updated cutting-edge technology company\",\n    \"location\": \"San Francisco, CA\",\n    \"service_category\": \"IT\",\n    \"rating\": 4.9\n}"}, "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Delete Company", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/companies/1", "host": ["{{base_url}}"], "path": ["api", "companies", "1"]}}, "response": []}, {"name": "Add User to Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2\n}"}, "url": {"raw": "{{base_url}}/api/companies/1/add-user", "host": ["{{base_url}}"], "path": ["api", "companies", "1", "add-user"]}}, "response": []}, {"name": "Remove User from Company", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 2\n}"}, "url": {"raw": "{{base_url}}/api/companies/1/remove-user", "host": ["{{base_url}}"], "path": ["api", "companies", "1", "remove-user"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}]}