# 🚀 Assessment App - Laravel API

A comprehensive Laravel API application with authentication, company management, search & filtering, and role-based authorization.

## 📋 **Features**

- ✅ **Laravel Sanctum Authentication** - Token-based API authentication
- ✅ **Company Management** - Full CRUD operations with role-based access
- ✅ **Advanced Search & Filtering** - Keyword search, service category, rating, location filters
- ✅ **Role-Based Authorization** - Admin, Vendor, and User roles with different permissions
- ✅ **Comprehensive API Testing** - Complete Postman collection included

## 🛠️ **Quick Setup**

### **Prerequisites**
- PHP 8.1+
- Composer
- MySQL/MariaDB
- XAMPP/WAMP (if using local development)

### **1. Clone & Install**
```bash
# Navigate to your web directory (e.g., XAMPP htdocs)
cd /Applications/XAMPP/xamppfiles/htdocs/

# Install dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### **2. Database Setup**
```bash
# Update .env with your database credentials
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=assessment_app
DB_USERNAME=root
DB_PASSWORD=

# Create database and run migrations
php artisan migrate

# Seed the database with sample data
php artisan db:seed
```

### **3. Start the Application**
```bash
# Start Laravel development server
php artisan serve

# Your API will be available at: http://127.0.0.1:8000
```

## 🔐 **Authentication & Roles**

### **Default Test Accounts**
| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| **🔴 Admin** | <EMAIL> | password123 | Full access - CRUD companies, manage users |
| **🟡 Vendor** | <EMAIL> | password123 | Limited - Create/update companies, manage associations |
| **🟢 User** | <EMAIL> | password123 | Read-only - View and search companies |

### **Authentication Flow**
1. **Login** → Get API token
2. **Use token** in `Authorization: Bearer {token}` header
3. **Access protected endpoints** based on your role

## 📡 **API Endpoints**

### **🔑 Authentication**
```bash
POST /api/auth/register     # Register new user
POST /api/auth/login        # Login and get token
POST /api/auth/logout       # Logout (revoke token)
GET  /api/auth/user         # Get user info
```

### **🏢 Company Management**
```bash
GET    /api/companies           # List companies (All users)
GET    /api/companies/{id}      # View company (All users)
POST   /api/companies           # Create company (Admin & Vendor)
PUT    /api/companies/{id}      # Update company (Admin & Vendor)
DELETE /api/companies/{id}      # Delete company (Admin only)
```

### **🔍 Search & Filtering**
```bash
# Search by keyword
GET /api/companies?search=tech

# Filter by service category
GET /api/companies?service_category=IT

# Filter by rating
GET /api/companies?rating=4.5

# Filter by location
GET /api/companies?location=New York

# Combined search with sorting
GET /api/companies?search=tech&service_category=IT&rating=4&sort_by=rating&sort_order=desc
```

### **🧪 Role-Based Testing**
```bash
GET /api/role-test/my-permissions      # Check your permissions
GET /api/role-test/admin-only          # Admin only endpoint
GET /api/role-test/vendor-only         # Vendor only endpoint
GET /api/role-test/user-only           # User only endpoint
```

## 🧪 **Testing with Postman**

### **1. Import Collection**
1. Open Postman
2. Click **Import**
3. Select `Assessment_App_APIs.postman_collection.json`
4. Import the collection

### **2. Set Environment**
1. Create new environment in Postman
2. Add variable: `base_url` = `http://127.0.0.1:8000`
3. Add variable: `auth_token` = (leave empty)

### **3. Test Authentication**
1. Use **"Login as Admin"** request
2. Token will be automatically saved
3. Test other endpoints with the saved token

### **4. Test Different Roles**
- **Login as Admin** → Test all endpoints
- **Login as Vendor** → Test limited access
- **Login as User** → Test read-only access

## 🔍 **Available Search Filters**

| Parameter | Description | Example |
|-----------|-------------|---------|
| `search` | Keyword search in name/description | `?search=technology` |
| `service_category` | Filter by service type | `?service_category=IT` |
| `rating` | Minimum rating filter | `?rating=4.5` |
| `location` | Partial location search | `?location=California` |
| `sort_by` | Sort field | `?sort_by=rating` |
| `sort_order` | Sort direction | `?sort_order=desc` |

### **Available Service Categories**
- IT
- Marketing
- Consulting
- Design

## 🛡️ **Security Features**

- **Token-based authentication** with Laravel Sanctum
- **Role-based authorization** with custom middleware
- **Input validation** on all endpoints
- **SQL injection protection** with Eloquent ORM
- **CORS protection** for frontend integration

## 📁 **Project Structure**

```
assessment-app/
├── app/Http/Controllers/
│   ├── Api/AuthController.php          # Authentication endpoints
│   ├── Api/RoleTestController.php      # Role-based testing
│   └── CompanyController.php           # Company CRUD operations
├── app/Http/Middleware/
│   └── CheckRole.php                   # Role-based authorization
├── app/Models/
│   ├── User.php                        # User model with role helpers
│   ├── Company.php                     # Company model
│   └── Role.php                        # Role model
├── routes/api.php                      # API routes definition
└── Assessment_App_APIs.postman_collection.json  # Postman collection
```

## 🚨 **Troubleshooting**

### **Common Issues**
- **500 Error**: Check `.env` database credentials
- **401 Unauthorized**: Verify token is included in Authorization header
- **403 Forbidden**: Check user role permissions
- **404 Not Found**: Ensure Laravel server is running

### **Debug Commands**
```bash
# Check routes
php artisan route:list

# Clear cache
php artisan cache:clear
php artisan config:clear

# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();
```

## 🎯 **Testing Scenarios**

1. **Authentication Test**: Register → Login → Access protected endpoint
2. **Role Test**: Login with different roles → Test access levels
3. **Search Test**: Use various search parameters → Verify filtering
4. **CRUD Test**: Create → Read → Update → Delete companies (based on role)

## 📚 **Additional Documentation**

- `ROLE_BASED_TESTING_GUIDE.md` - Detailed role testing guide
- `SEARCH_FEATURES_SUMMARY.md` - Search functionality overview
- `POSTMAN_COLLECTION_README.md` - Postman usage instructions

## 🎉 **You're Ready!**

Your Assessment App API is now fully configured with:
- ✅ Authentication system
- ✅ Role-based authorization  
- ✅ Company management
- ✅ Advanced search & filtering
- ✅ Comprehensive testing tools

Start testing with the Postman collection and explore all the features! 🚀
