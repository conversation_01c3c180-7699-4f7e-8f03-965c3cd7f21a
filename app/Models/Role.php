<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = ['role_name'];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    // Role constants for easy reference
    const ADMIN = 'admin';
    const VENDOR = 'vendor';
    const USER = 'user';
}
