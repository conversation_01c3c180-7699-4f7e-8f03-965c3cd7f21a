<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }


    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function companies()
    {
        return $this->belongsToMany(Company::class, 'company_user');
    }

    // Role helper methods
    public function hasRole($role)
    {
        return $this->role->role_name === $role;
    }

    public function isAdmin()
    {
        return $this->hasRole(Role::ADMIN);
    }

    public function isVendor()
    {
        return $this->hasRole(Role::VENDOR);
    }

    public function isUser()
    {
        return $this->hasRole(Role::USER);
    }

    public function canManageCompanies()
    {
        return $this->isAdmin();
    }

    public function canViewCompanies()
    {
        return $this->isAdmin() || $this->isVendor() || $this->isUser();
    }

    public function canManageUsers()
    {
        return $this->isAdmin();
    }
}
