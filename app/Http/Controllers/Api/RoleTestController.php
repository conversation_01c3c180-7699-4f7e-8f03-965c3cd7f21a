<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;

class RoleTestController extends Controller
{
    /**
     * Test endpoint accessible by all authenticated users
     */
    public function allUsers(Request $request)
    {
        return response()->json([
            'message' => 'Success! This endpoint is accessible by all authenticated users.',
            'user' => $request->user()->only(['id', 'name', 'email']),
            'role' => $request->user()->role->role_name,
            'permissions' => [
                'can_view_companies' => $request->user()->canViewCompanies(),
                'can_manage_companies' => $request->user()->canManageCompanies(),
                'can_manage_users' => $request->user()->canManageUsers(),
            ],
            'timestamp' => now()
        ]);
    }

    /**
     * Test endpoint accessible only by admins
     */
    public function adminOnly(Request $request)
    {
        return response()->json([
            'message' => 'Success! This is an admin-only endpoint.',
            'user' => $request->user()->only(['id', 'name', 'email']),
            'role' => $request->user()->role->role_name,
            'admin_features' => [
                'can_create_companies' => true,
                'can_delete_companies' => true,
                'can_manage_all_users' => true,
                'can_view_system_stats' => true,
            ],
            'timestamp' => now()
        ]);
    }

    /**
     * Test endpoint accessible by admins and vendors
     */
    public function adminAndVendor(Request $request)
    {
        return response()->json([
            'message' => 'Success! This endpoint is accessible by admins and vendors.',
            'user' => $request->user()->only(['id', 'name', 'email']),
            'role' => $request->user()->role->role_name,
            'features' => [
                'can_edit_companies' => $request->user()->isAdmin() || $request->user()->isVendor(),
                'can_add_users_to_companies' => true,
                'can_view_company_details' => true,
            ],
            'timestamp' => now()
        ]);
    }

    /**
     * Test endpoint accessible only by vendors
     */
    public function vendorOnly(Request $request)
    {
        return response()->json([
            'message' => 'Success! This is a vendor-only endpoint.',
            'user' => $request->user()->only(['id', 'name', 'email']),
            'role' => $request->user()->role->role_name,
            'vendor_features' => [
                'can_manage_own_companies' => true,
                'can_view_assigned_companies' => true,
                'can_update_company_info' => true,
            ],
            'timestamp' => now()
        ]);
    }

    /**
     * Test endpoint accessible only by regular users
     */
    public function userOnly(Request $request)
    {
        return response()->json([
            'message' => 'Success! This is a regular user-only endpoint.',
            'user' => $request->user()->only(['id', 'name', 'email']),
            'role' => $request->user()->role->role_name,
            'user_features' => [
                'can_view_companies' => true,
                'can_search_companies' => true,
                'can_view_company_details' => true,
                'can_filter_companies' => true,
            ],
            'timestamp' => now()
        ]);
    }

    /**
     * Get user's role information and permissions
     */
    public function myPermissions(Request $request)
    {
        $user = $request->user();
        
        return response()->json([
            'user' => $user->only(['id', 'name', 'email']),
            'role' => $user->role->role_name,
            'permissions' => [
                'is_admin' => $user->isAdmin(),
                'is_vendor' => $user->isVendor(),
                'is_user' => $user->isUser(),
                'can_view_companies' => $user->canViewCompanies(),
                'can_manage_companies' => $user->canManageCompanies(),
                'can_manage_users' => $user->canManageUsers(),
            ],
            'accessible_endpoints' => $this->getAccessibleEndpoints($user),
            'timestamp' => now()
        ]);
    }

    /**
     * Get list of companies based on user role
     */
    public function roleBasedCompanies(Request $request)
    {
        $user = $request->user();
        
        if ($user->isAdmin()) {
            // Admins can see all companies with full details
            $companies = Company::with('service')->get();
            $message = 'Admin view: All companies with full access';
        } elseif ($user->isVendor()) {
            // Vendors can see companies they're associated with
            $companies = $user->companies()->with('service')->get();
            $message = 'Vendor view: Your associated companies';
        } else {
            // Regular users can see basic company info (limited fields)
            $companies = Company::with('service')->get()->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'location' => $company->location,
                    'rating' => $company->rating,
                    'service_category' => $company->service->service_name ?? 'Unknown'
                ];
            });
            $message = 'User view: Public company information';
        }

        return response()->json([
            'message' => $message,
            'user_role' => $user->role->role_name,
            'companies_count' => count($companies),
            'companies' => $companies,
            'timestamp' => now()
        ]);
    }

    /**
     * Get accessible endpoints based on user role
     */
    private function getAccessibleEndpoints($user)
    {
        $endpoints = [
            'all_users' => '/api/role-test/all-users',
            'my_permissions' => '/api/role-test/my-permissions',
            'role_based_companies' => '/api/role-test/role-based-companies',
        ];

        if ($user->isAdmin()) {
            $endpoints['admin_only'] = '/api/role-test/admin-only';
            $endpoints['admin_and_vendor'] = '/api/role-test/admin-and-vendor';
        }

        if ($user->isVendor()) {
            $endpoints['vendor_only'] = '/api/role-test/vendor-only';
            $endpoints['admin_and_vendor'] = '/api/role-test/admin-and-vendor';
        }

        if ($user->isUser()) {
            $endpoints['user_only'] = '/api/role-test/user-only';
        }

        return $endpoints;
    }
}
