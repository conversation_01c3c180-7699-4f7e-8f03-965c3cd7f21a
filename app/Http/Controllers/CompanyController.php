<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\CompanyResource;

class CompanyController extends Controller
{
    public function __construct()
    {
        // Note: Role-based authorization is now handled at the route level
        // or can be implemented using Laravel's Gate/Policy system
        // The routes are already protected by auth:sanctum middleware
    }

    /**
     * Display a listing of the companies.
     * Pagination is used to handle large records efficiently.
     *
     * @param Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Request $request)
    {
        $query = Company::query();

        // Apply filtering based on the request parameters
        if ($request->has('service_category')) {
            $service = Service::where('service_name', $request->service_category)->first();
            $query->where('service_id', $service->id);
        }

        if ($request->has('rating')) {
            $query->where('rating', '>=', $request->rating);
        }

        if ($request->has('location')) {
            $query->where('location', 'LIKE', "%{$request->location}%");
        }

        $companies = $query->paginate(20);
        return CompanyResource::collection($companies);
    }

    /**
     * Display the specified company by ID.
     *
     * @param  int  $id
     * @return CompanyResource
     */
    public function show($id)
    {
        $company = Company::findOrFail($id);

        return new CompanyResource($company);
    }

    /**
     * Store a newly created company in the database.
     * Only accessible by an admin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return CompanyResource
     */
    public function store(Request $request)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string',
            'service_category' => 'required|string',
            'rating' => 'required|numeric|min:1|max:5',
        ]);

        // Retrieve the service ID based on the provided service category
        $service = Service::where('service_name', $validatedData['service_category'])->first();

        $company = Company::create([
            'name' => $validatedData['name'],
            'description' => $validatedData['description'],
            'location' => $validatedData['location'],
            'service_id' => $service->id,
            'rating' => $validatedData['rating'],
        ]);

        return new CompanyResource($company);
    }

    /**
     * Update the specified company.
     * Only accessible by an admin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return CompanyResource
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string',
            'service_category' => 'required|string',
            'rating' => 'required|numeric|min:1|max:5',
        ]);

        $service = Service::where('service_name', $validatedData['service_category'])->first();

        $company = Company::findOrFail($id);

        $company->update([
            'name' => $validatedData['name'],
            'description' => $validatedData['description'],
            'location' => $validatedData['location'],
            'service_id' => $service->id,
            'rating' => $validatedData['rating'],
        ]);

        return new CompanyResource($company);
    }

    /**
     * Remove the specified company from the database.
     * Only accessible by an admin.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $company = Company::findOrFail($id);

        $company->delete();

        return response()->json(null, 204);
    }

    /**
     * Add a user to the company.
     * Only accessible by an admin or vendor.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $companyId
     * @return \Illuminate\Http\JsonResponse
     */
    public function addUserToCompany(Request $request, $companyId)
    {
        $validatedData = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $company = Company::findOrFail($companyId);

        $user = User::findOrFail($validatedData['user_id']);

        $company->users()->attach($user->id);

        return response()->json(['message' => 'User added to company successfully']);
    }

    /**
     * Remove a user from the company.
     * Only accessible by an admin or vendor.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $companyId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeUserFromCompany(Request $request, $companyId)
    {
        $validatedData = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $company = Company::findOrFail($companyId);

        $user = User::findOrFail($validatedData['user_id']);

        $company->users()->detach($user->id);

        return response()->json(['message' => 'User removed from company successfully']);
    }
}
