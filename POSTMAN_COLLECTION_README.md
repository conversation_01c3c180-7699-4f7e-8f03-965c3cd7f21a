# Laravel Sanctum API - Postman Collection

This Postman collection provides a complete set of API endpoints for testing your Laravel Sanctum implementation.

## 📁 Collection Contents

### 🔐 Authentication Endpoints
- **Register User** - Create a new user account
- **Login User** - Authenticate and receive API token
- **Get User Info** - Retrieve authenticated user details
- **Logout User** - Revoke current session token
- **Revoke All Tokens** - Revoke all user tokens

### 🧪 Test Endpoints
- **Test Authentication** - Verify token-based authentication

### 🏢 Company Management
- **Get All Companies** - List all companies with optional filters
- **Get Company by ID** - Retrieve specific company details
- **Create Company** - Add new company (admin only)
- **Update Company** - Modify company details (admin only)
- **Delete Company** - Remove company (admin only)
- **Add User to Company** - Associate user with company
- **Remove User from Company** - Remove user association

## 🚀 How to Import and Use

### Step 1: Import Collection
1. Open Postman
2. Click **Import** button
3. Select **Upload Files**
4. Choose `Laravel_Sanctum_API.postman_collection.json`
5. Click **Import**

### Step 2: Set Up Environment (Optional but Recommended)
1. Create a new environment in Postman
2. Add variable: `base_url` = `http://127.0.0.1:8000`
3. Add variable: `auth_token` = (leave empty, will be auto-populated)

### Step 3: Start Your Laravel Server
```bash
php artisan serve
```

### Step 4: Test the API

#### 🔑 Authentication Flow
1. **First, Login:**
   - Use "Login User" request
   - Default credentials: `<EMAIL>` / `password123`
   - The token will be automatically saved to `{{auth_token}}` variable

2. **Test Protected Endpoints:**
   - All other requests use the saved token automatically
   - Token is included in Authorization header as `Bearer {{auth_token}}`

## 📋 Request Examples

### Login Request Body
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

### Register Request Body
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "role_id": 1
}
```

### Create Company Request Body
```json
{
    "name": "New Tech Company",
    "description": "A cutting-edge technology company",
    "location": "Silicon Valley, CA",
    "service_category": "IT",
    "rating": 4.8
}
```

## 🔧 Available Query Parameters

### Get All Companies - Search & Filtering
- `search` - Keyword search across company name and description
- `service_category` - Filter by service type (IT, Marketing, Consulting, Design)
- `rating` - Minimum rating filter (1-5, supports decimals)
- `location` - Location search (partial match, case-insensitive)
- `sort_by` - Sort field (name, rating, location, created_at)
- `sort_order` - Sort direction (asc, desc)

### Search Examples:
- **Keyword Search:** `GET /api/companies?search=tech`
- **Service Filter:** `GET /api/companies?service_category=IT`
- **Rating Filter:** `GET /api/companies?rating=4.5`
- **Location Filter:** `GET /api/companies?location=New York`
- **Combined Search:** `GET /api/companies?search=tech&service_category=IT&rating=4&location=CA&sort_by=rating&sort_order=desc`

### Available Service Categories:
- IT
- Marketing
- Consulting
- Design

## 🛡️ Authentication Notes

- **Public Endpoints:** Register, Login
- **Protected Endpoints:** All others require `Authorization: Bearer {token}`
- **Token Management:** Login automatically saves token, logout revokes it
- **Token Persistence:** Tokens persist until explicitly revoked or expired

## 🎯 Testing Workflow

1. **Start Fresh:** Use "Register User" or "Login User"
2. **Verify Auth:** Run "Test Authentication" to confirm token works
3. **Test CRUD:** Use company management endpoints
4. **Clean Up:** Use "Logout User" or "Revoke All Tokens" when done

## 🚨 Troubleshooting

### Common Issues:
- **401 Unauthorized:** Check if token is valid and properly set
- **422 Validation Error:** Verify request body format and required fields
- **404 Not Found:** Ensure Laravel server is running and routes are correct
- **500 Server Error:** Check Laravel logs for detailed error information

### Debug Tips:
- Check Postman Console for request/response details
- Verify `{{base_url}}` variable is set correctly
- Ensure Laravel server is running on correct port
- Check that database migrations have been run

## 📝 Default Test Data

The collection includes sample data for testing:
- **Admin User:** <EMAIL> / password123
- **Vendor User:** <EMAIL> / password123
- **Regular User:** <EMAIL> / password123

## 🔄 Auto-Token Management

The collection includes a test script that automatically:
- Extracts token from login response
- Saves it to `{{auth_token}}` environment variable
- Uses it in subsequent requests

No manual token copying required! 🎉
