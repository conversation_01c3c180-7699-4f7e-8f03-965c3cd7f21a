# 🔐 Role-Based Authorization Testing Guide

## 🎯 **Overview**

Your Laravel Sanctum API now includes comprehensive role-based authorization with dedicated testing endpoints to demonstrate different access levels and permissions.

## 👥 **User Roles & Permissions**

### **🔴 Admin (role_id: 1)**
- **Full Access** - Can perform all operations
- **Permissions:**
  - ✅ View all companies
  - ✅ Create/Update/Delete companies
  - ✅ Manage all users
  - ✅ Access admin-only features
  - ✅ Access admin & vendor features

### **🟡 Vendor (role_id: 2)**
- **Limited Management** - Can manage companies they're associated with
- **Permissions:**
  - ✅ View companies (their associated ones)
  - ✅ Create/Update companies
  - ❌ Delete companies
  - ✅ Manage company-user relationships
  - ✅ Access vendor-only features
  - ✅ Access admin & vendor features

### **🟢 User (role_id: 3)**
- **Read-Only Access** - Can only view public information
- **Permissions:**
  - ✅ View companies (limited public info)
  - ✅ Search and filter companies
  - ❌ Create/Update/Delete companies
  - ❌ Manage users
  - ✅ Access user-only features

## 🧪 **Role-Based Testing Endpoints**

### **📋 Available Test Accounts:**
- **Admin:** <EMAIL> / password123
- **Vendor:** <EMAIL> / password123
- **User:** <EMAIL> / password123

### **🔍 Testing Endpoints:**

#### **1. Universal Access (All Authenticated Users)**
```
GET /api/role-test/all-users
GET /api/role-test/my-permissions
GET /api/role-test/role-based-companies
```

#### **2. Admin Only**
```
GET /api/role-test/admin-only
```

#### **3. Admin & Vendor**
```
GET /api/role-test/admin-and-vendor
```

#### **4. Vendor Only**
```
GET /api/role-test/vendor-only
```

#### **5. User Only**
```
GET /api/role-test/user-only
```

## 🏢 **Company Management Permissions**

### **📖 Read Operations (All Users)**
```
GET /api/companies              # List companies
GET /api/companies/{id}         # View company details
```

### **✏️ Write Operations (Admin & Vendor)**
```
POST /api/companies             # Create company
PUT/PATCH /api/companies/{id}   # Update company
POST /api/companies/{id}/add-user    # Add user to company
POST /api/companies/{id}/remove-user # Remove user from company
```

### **🗑️ Delete Operations (Admin Only)**
```
DELETE /api/companies/{id}      # Delete company
```

## 🎯 **Testing Scenarios**

### **Scenario 1: Admin Full Access Test**
1. Login as admin
2. Test all endpoints (should succeed)
3. Verify admin-only features work
4. Test company CRUD operations

### **Scenario 2: Vendor Limited Access Test**
1. Login as vendor
2. Test vendor-allowed endpoints (should succeed)
3. Try admin-only endpoint (should fail with 403)
4. Test company create/update (should succeed)
5. Try company delete (should fail with 403)

### **Scenario 3: User Read-Only Test**
1. Login as user
2. Test user-allowed endpoints (should succeed)
3. Try admin/vendor endpoints (should fail with 403)
4. Test company viewing (should succeed)
5. Try company creation (should fail with 403)

### **Scenario 4: Cross-Role Access Denial**
1. Login as any role
2. Try accessing endpoints not allowed for that role
3. Verify proper 403 responses with clear error messages

## 📊 **Expected Responses**

### **✅ Success Response Example:**
```json
{
    "message": "Success! This is an admin-only endpoint.",
    "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
    },
    "role": "admin",
    "admin_features": {
        "can_create_companies": true,
        "can_delete_companies": true,
        "can_manage_all_users": true,
        "can_view_system_stats": true
    },
    "timestamp": "2025-06-12T17:21:55.732378Z"
}
```

### **❌ Access Denied Response Example:**
```json
{
    "message": "Access denied. Required role(s): admin. Your role: vendor",
    "required_roles": ["admin"],
    "user_role": "vendor"
}
```

### **🔍 Permissions Check Response:**
```json
{
    "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
    },
    "role": "admin",
    "permissions": {
        "is_admin": true,
        "is_vendor": false,
        "is_user": false,
        "can_view_companies": true,
        "can_manage_companies": true,
        "can_manage_users": true
    },
    "accessible_endpoints": {
        "all_users": "/api/role-test/all-users",
        "my_permissions": "/api/role-test/my-permissions",
        "role_based_companies": "/api/role-test/role-based-companies",
        "admin_only": "/api/role-test/admin-only",
        "admin_and_vendor": "/api/role-test/admin-and-vendor"
    },
    "timestamp": "2025-06-12T17:21:49.275576Z"
}
```

## 🔧 **Technical Implementation**

### **Custom Middleware: `CheckRole`**
- Validates user authentication
- Checks user role against required roles
- Returns detailed error messages for access denial
- Supports multiple role requirements

### **User Model Enhancements:**
- `hasRole($role)` - Check specific role
- `isAdmin()`, `isVendor()`, `isUser()` - Role shortcuts
- `canManageCompanies()` - Permission checks
- `canViewCompanies()` - Access validation

### **Route Protection:**
```php
// Single role
Route::middleware('role:admin')->group(function () {
    Route::get('/admin-only', [Controller::class, 'adminOnly']);
});

// Multiple roles
Route::middleware('role:admin,vendor')->group(function () {
    Route::post('/companies', [Controller::class, 'store']);
});
```

## 🎉 **Testing Results**

All role-based features have been tested and verified:
- ✅ Admin can access all endpoints
- ✅ Vendor can access vendor and shared endpoints
- ✅ User can access user and public endpoints
- ✅ Access denial works correctly with proper error messages
- ✅ Role-based company data filtering works
- ✅ Permission checking endpoint provides accurate information

## 🚀 **Production Ready**

The role-based authorization system is:
- **Secure** - Proper middleware validation
- **Flexible** - Easy to extend with new roles
- **Clear** - Detailed error messages and permission info
- **Testable** - Comprehensive test endpoints
- **Well-Documented** - Complete Postman collection included

Your API now provides enterprise-level role-based access control! 🔐
