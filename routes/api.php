<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\Api\AuthController;

// Public authentication routes
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/login', [AuthController::class, 'login']);

// Protected routes that require authentication
Route::middleware('auth:sanctum')->group(function () {
    // Authentication routes
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    Route::post('/auth/revoke-all-tokens', [AuthController::class, 'revokeAllTokens']);

    // Company resource routes
    Route::resource('companies', CompanyController::class);

    // Additional company routes
    Route::post('companies/{companyId}/add-user', [CompanyController::class, 'addUserToCompany']);
    Route::post('companies/{companyId}/remove-user', [CompanyController::class, 'removeUserFromCompany']);
});

